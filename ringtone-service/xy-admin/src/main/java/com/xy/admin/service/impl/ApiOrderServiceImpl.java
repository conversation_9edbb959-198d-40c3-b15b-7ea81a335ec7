package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ApiOrder;
import com.xy.admin.mapper.ApiOrderMapper;
import com.xy.admin.service.ApiOrderService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;
import org.springframework.beans.BeanUtils;

import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 营销推广订单综合表(api_order)表服务实现
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
@Service
public class ApiOrderServiceImpl extends ServiceImpl<ApiOrderMapper, ApiOrder> implements ApiOrderService{

    @Override
    public IPage<ApiOrderVO> queryPage(ApiOrderQueryVO queryVO) {
        // 构建查询条件
        LambdaQueryWrapper<ApiOrder> wrapper = new LambdaQueryWrapper<>();

        // 应用ID查询
        if (StrUtil.isNotBlank(queryVO.getAppId())) {
            wrapper.like(ApiOrder::getAppId, queryVO.getAppId());
        }

        // 渠道编号查询
        if (StrUtil.isNotBlank(queryVO.getChannelNo())) {
            wrapper.like(ApiOrder::getChannelNo, queryVO.getChannelNo());
        }

        // 产品编号查询
        if (StrUtil.isNotBlank(queryVO.getProductNo())) {
            wrapper.like(ApiOrder::getProductNo, queryVO.getProductNo());
        }

        // 手机号码查询
        if (StrUtil.isNotBlank(queryVO.getMobileNo())) {
            wrapper.like(ApiOrder::getMobileNo, queryVO.getMobileNo());
        }

        // 订单ID查询
        if (StrUtil.isNotBlank(queryVO.getOrderId())) {
            wrapper.like(ApiOrder::getOrderId, queryVO.getOrderId());
        }

        // 订单状态码查询
        if (StrUtil.isNotBlank(queryVO.getOrderStatus())) {
            wrapper.eq(ApiOrder::getOrderStatus, queryVO.getOrderStatus());
        }

        // 外部订单状态查询
        if (StrUtil.isNotBlank(queryVO.getOutOrderStatus())) {
            wrapper.eq(ApiOrder::getOutOrderStatus, queryVO.getOutOrderStatus());
        }

        // 外部订单ID查询
        if (StrUtil.isNotBlank(queryVO.getOutOrderId())) {
            wrapper.like(ApiOrder::getOutOrderId, queryVO.getOutOrderId());
        }

        // 操作类型查询
        if (StrUtil.isNotBlank(queryVO.getOperationType())) {
            wrapper.eq(ApiOrder::getOperationType, queryVO.getOperationType());
        }

        // 客户端IP查询
        if (StrUtil.isNotBlank(queryVO.getClientIp())) {
            wrapper.like(ApiOrder::getClientIp, queryVO.getClientIp());
        }

        // 应用包名查询
        if (StrUtil.isNotBlank(queryVO.getAppPackage())) {
            wrapper.like(ApiOrder::getAppPackage, queryVO.getAppPackage());
        }

        // 应用名称查询
        if (StrUtil.isNotBlank(queryVO.getAppName())) {
            wrapper.like(ApiOrder::getAppName, queryVO.getAppName());
        }

        // 平台名称查询
        if (StrUtil.isNotBlank(queryVO.getPlatform())) {
            wrapper.like(ApiOrder::getPlatform, queryVO.getPlatform());
        }

        // 服务器查询
        if (StrUtil.isNotBlank(queryVO.getServer())) {
            wrapper.like(ApiOrder::getServer, queryVO.getServer());
        }

        // 创建时间范围查询
        if (queryVO.getCreateTimeStart() != null) {
            wrapper.ge(ApiOrder::getCreateTime,
                java.util.Date.from(queryVO.getCreateTimeStart().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (queryVO.getCreateTimeEnd() != null) {
            wrapper.le(ApiOrder::getCreateTime,
                java.util.Date.from(queryVO.getCreateTimeEnd().atZone(ZoneId.systemDefault()).toInstant()));
        }

        // 更新时间范围查询
        if (queryVO.getUpdateTimeStart() != null) {
            wrapper.ge(ApiOrder::getUpdateTime,
                java.util.Date.from(queryVO.getUpdateTimeStart().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (queryVO.getUpdateTimeEnd() != null) {
            wrapper.le(ApiOrder::getUpdateTime,
                java.util.Date.from(queryVO.getUpdateTimeEnd().atZone(ZoneId.systemDefault()).toInstant()));
        }

        // 通用关键字查询（手机号、订单ID、外部订单ID）
        if (StrUtil.isNotBlank(queryVO.getKeyword())) {
            wrapper.and(w -> w.like(ApiOrder::getMobileNo, queryVO.getKeyword())
                    .or().like(ApiOrder::getOrderId, queryVO.getKeyword())
                    .or().like(ApiOrder::getOutOrderId, queryVO.getKeyword()));
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(ApiOrder::getCreateTime);

        // 分页查询
        Page<ApiOrder> page = queryVO.buildPage();
        IPage<ApiOrder> pageResult = this.page(page, wrapper);

        // 转换为VO
        List<ApiOrderVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建返回结果
        Page<ApiOrderVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 实体转VO
     */
    private ApiOrderVO convertToVO(ApiOrder entity) {
        ApiOrderVO vo = new ApiOrderVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
